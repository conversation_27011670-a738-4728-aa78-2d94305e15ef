import 'package:dio/dio.dart' as DIO;
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';
import 'package:path/path.dart';

class ProductController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();
  int saleMenuId = 0;

  //function
  getSaleMenu() async {
    try {
      final response = await dioClient.get(
        SmeUrl.menuTree,
      );
      dynamic data = response.data;
      if (data['Success'] &&
          data['Result'] != null &&
          data['Result']['TimeSaleMenus'] != null) {
        List menus = data['Result']['TimeSaleMenus'];
        if (menus.isNotEmpty) {
          saleMenuId = menus[0]['TimeSaleAutoId'];
        } else {
          Get.snackbar('Lỗi', 'Không có menu bán hàng nào');
        }
      } else {
        Get.snackbar('Lỗi',
            'Không thể lấy thông tin menu: ${data['Message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      Get.snackbar('Lỗi', 'Lỗi kết nối khi lấy menu: $e');
    }
  }

  getUnits() async {
    List list = [];
    try {
      final response = await dioClient.get(
        SmeUrl.units,
      );
      dynamic data = response.data;

      if (data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {}
    return list;
  }

  getCategories() async {
    List list = [];
    try {
      final response = await dioClient.get(
        '${SmeUrl.groups}?timeSaleId=$saleMenuId',
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {}
    return list;
  }

  getProductWithCategories() async {
    List list = [];
    try {
      final response = await dioClient.get(
        SmeUrl.menuTree,
      );
      dynamic data = response.data;
      if (data['Success'] &&
          data['Result'] != null &&
          data['Result']['TimeSaleMenus'] != null &&
          data['Result']['TimeSaleMenus'].isNotEmpty) {
        list = data['Result']['TimeSaleMenus'][0]['ItemGroups'];
      }
      // ignore: empty_catches
    } catch (e) {}
    return list;
  }

  postCreateOrUpdateCategory(id, String name, vatTaxPer, piTaxPer) async {
    dynamic body = {
      "Id": id,
      "TimeSaleAutoId": saleMenuId,
      "ItemGroupName": name,
      "AvailableStatus": "AVAILABLE",
      "VatTaxPer": vatTaxPer,
      "PITaxPer": piTaxPer,
      "IsActive": true,
    };

    try {
      final response =
          await dioClient.post(SmeUrl.createOrUpdateGroup, data: body);
      dynamic data = response.data;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  postDeleteCategory(id) async {
    try {
      final response = await dioClient.delete(
        '${SmeUrl.deleteGroup}?Id=$id',
      );
      dynamic data = response;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  getDetailProduct(id) async {
    dynamic item;
    try {
      final response = await dioClient.get(
        '${SmeUrl.detailItem}?ItemId=$id',
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        item = data['Result'];
      }
    } catch (e) {}
    return item;
  }

  postCreateOrUpdateProduct(
      id,
      name,
      originalPrice,
      price,
      categoryId,
      vatPercent,
      status,
      includeVat,
      unitId,
      unitName,
      barcode,
      isActive,
      groupPITaxPer,
      groupVatTaxPer,
      itemNo) async {
    int productId = 0;

    // Convert string numbers to actual numbers for API
    dynamic finalOriginalPrice = originalPrice;
    dynamic finalPrice = price;
    dynamic finalVatPercent = vatPercent;

    // If they are strings (from normal UI), convert to numbers
    if (originalPrice is String) {
      finalOriginalPrice = double.tryParse(originalPrice) ?? 0.0;
    }
    if (price is String) {
      finalPrice = double.tryParse(price) ?? 0.0;
    }
    if (vatPercent is String) {
      finalVatPercent = double.tryParse(vatPercent) ?? 0.0;
    }

    dynamic body = {
      "Id": id,
      "MenuId": 1, // Add MenuId like successful request
      "TimeSaleMenuId": saleMenuId,
      "ItemGroupId": categoryId,
      "Barcode": barcode,
      "ItemId": null,
      "ItemNo": itemNo != '' ? itemNo : AppFunction.generateRandomString(10),
      "ItemName": name,
      "ItemDesc": null,
      "IsCombo": false,
      "UnitId": unitId,
      "VatPercent": finalVatPercent, // Send as number
      "CostPrice": finalOriginalPrice, // Send as number
      "Price": finalPrice, // Send as number
      "IncludeVat": includeVat,
      "AvailableStatus": status,
      "CreatedById": null,
      "IsActive": isActive,
      "GroupPITaxPer": groupPITaxPer,
      "GroupVatTaxPer": groupVatTaxPer,
      "Msg": null,
    };

    try {
      final response =
          await dioClient.post(SmeUrl.createOrUpdateItem, data: body);
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        productId = data['Result']['Id'];
        AppFunction.showSuccess(data['Message']);
      } else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi thêm sản phẩm');
    }
    return productId;
  }

  // Create product from import with category and unit names
  Future<bool> createProductFromImport(
    String productName,
    String categoryName,
    String unitName,
    double price,
    double originalPrice,
    double vatPercent,
    bool includeVat,
    int status,
    String? barcode,
  ) async {
    try {
      // Ensure saleMenuId is set
      if (saleMenuId == 0) {
        await getSaleMenu();
        if (saleMenuId == 0) {
          Get.snackbar('Lỗi', 'Không thể lấy thông tin menu bán hàng');
          return false;
        }
      }
      // Find or create category
      int categoryId = await _findOrCreateCategory(categoryName);
      if (categoryId == 0) {
        return false;
      }

      // Find or create unit
      int unitId = await _findOrCreateUnit(unitName);
      if (unitId == 0) {
        return false;
      }

      // Create product - use same format as successful request
      int productId = await postCreateOrUpdateProduct(
        null, // id
        productName, // name
        originalPrice, // originalPrice as double
        price, // price as double
        categoryId, // categoryId
        vatPercent, // vatPercent as double
        status == 1 ? "AVAILABLE" : "UNAVAILABLE", // status as string
        includeVat, // includeVat
        unitId, // unitId
        unitName, // unitName
        barcode ?? '', // barcode
        true, // isActive
        0, // groupPITaxPer
        0, // groupVatTaxPer
        '', // itemNo
      );

      return productId > 0;
    } catch (e) {
      return false;
    }
  }

  // Find existing category or create new one
  Future<int> _findOrCreateCategory(String categoryName) async {
    try {
      // Get existing categories
      List categories = await getCategories();

      // Find existing category
      for (var category in categories) {
        if (category['ItemGroupName']?.toString().toLowerCase() ==
            categoryName.toLowerCase()) {
          return category['Id'] ?? 0;
        }
      }

      // Create new category if not found
      bool success =
          await postCreateOrUpdateCategory(null, categoryName, '0', '0');
      if (success) {
        // Get updated categories and find the new one
        categories = await getCategories();
        for (var category in categories) {
          if (category['ItemGroupName']?.toString().toLowerCase() ==
              categoryName.toLowerCase()) {
            return category['Id'] ?? 0;
          }
        }
      }

      return 0;
    } catch (e) {
      return 0;
    }
  }

  // Find existing unit or create new one
  Future<int> _findOrCreateUnit(String unitName) async {
    try {
      // Get existing units
      List units = await getUnits();

      if (units.isEmpty) {
        Get.snackbar('Lỗi', 'Không có đơn vị nào trong hệ thống');
        return 0;
      }

      // Find existing unit (exact match first) - using correct field names
      for (var unit in units) {
        if (unit['UOM_NAME']?.toString().toLowerCase() ==
            unitName.toLowerCase()) {
          return unit['UOM_AUTOID'] ?? 0;
        }
      }

      // Try to find similar units
      List<String> commonUnits = [
        'cái',
        'chiếc',
        'kg',
        'gram',
        'lít',
        'ml',
        'hộp',
        'thùng',
        'bao',
        'gói',
        'bento',
        'bịch'
      ];
      String lowerUnitName = unitName.toLowerCase();

      for (var commonUnit in commonUnits) {
        if (lowerUnitName.contains(commonUnit)) {
          for (var unit in units) {
            String? unitNameStr = unit['UOM_NAME']?.toString().toLowerCase();
            if (unitNameStr != null && unitNameStr.contains(commonUnit)) {
              return unit['UOM_AUTOID'] ?? 0;
            }
          }
        }
      }

      // Last resort: use the first available unit
      if (units.isNotEmpty) {
        return units.first['UOM_AUTOID'] ?? 0;
      }

      return 0;
    } catch (e) {
      return 0;
    }
  }

  postDeleteProduct(id) async {
    try {
      final response = await dioClient.delete(
        '${SmeUrl.deleteItem}?Id=$id',
      );
      dynamic data = response;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  postUploadImageProduct(image) async {
    String path = '';

    String fileName = basename(image.path);
    DIO.FormData formData = DIO.FormData.fromMap({
      'file': await DIO.MultipartFile.fromFile(image.path, filename: fileName),
    });

    try {
      final response = await dioClient.post(
        SmeUrl.uploadImage,
        data: formData,
        options: DIO.Options(
          contentType: 'multipart/+-data',
        ),
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        AppFunction.showSuccess(data['Message']);
        path = data['Result']['FileUrl'];
      } else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError(e.toString());
    }
    return path;
  }

  postAddImageForProduct(productId, imagePath) async {
    try {
      final response = await dioClient.post(SmeUrl.createProductImage, data: {
        "MediaUrl": imagePath,
        "ItemMapperAutoId": productId,
        "IsActive": true,
      });
      dynamic data = response.data;

      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      } else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi xảy ra khi gán ảnh');
      return false;
    }
  }
}
